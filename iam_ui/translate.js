// COPYRIGHT (C) 2018-2021 GIG.TECH NV
// ALL RIGHTS RESERVED.
//
// ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
// CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
// TO MODIFY, REP<PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
// EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
// WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
//
// THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
// PUBLICATION OF SUCH SOURCE CODE.
//
// @@license_version:1.9@@

const fs = require('fs');
const axios = require('axios');

// OpenAI API Key (You should store this in an environment variable)
const OPENAI_API_KEY = "********************************************************";

if (!OPENAI_API_KEY) {
  console.error('Error: OPENAI_API_KEY environment variable is not set');
  process.exit(1);
}

// Rate limiting: delay between API calls to avoid hitting rate limits
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const translateText = async (text, targetLanguage) => {
  try {
    // Add a small delay to avoid rate limiting
    await delay(100);

    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-4o-mini', // Better model, still cost-effective
        messages: [
          {
            role: 'system',
            content: 'You are a professional translator. Translate the given text accurately while preserving any technical terms, placeholders, or formatting. Return only the translated text without any additional formatting or symbols.',
          },
          {
            role: 'user',
            content: `Translate the following text to ${targetLanguage}: ${text}`,
          },
        ],
        temperature: 0.3, // Lower temperature for more consistent translations
      },
      {
        headers: {
          Authorization: `Bearer ${OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return response.data.choices[0].message.content.trim();
  } catch (error) {
    console.error('Error translating text:', error.response?.data || error.message);

    // If it's a rate limit error, wait and retry once
    if (error.response?.status === 429) {
      console.log('Rate limit hit, waiting 2 seconds and retrying...');
      await delay(2000);
      try {
        const retryResponse = await axios.post(
          'https://api.openai.com/v1/chat/completions',
          {
            model: 'gpt-4o-mini',
            messages: [
              {
                role: 'system',
                content: 'You are a professional translator. Translate the given text accurately while preserving any technical terms, placeholders, or formatting. Return only the translated text without any additional formatting or symbols.',
              },
              {
                role: 'user',
                content: `Translate the following text to ${targetLanguage}: ${text}`,
              },
            ],
            temperature: 0.3,
          },
          {
            headers: {
              Authorization: `Bearer ${OPENAI_API_KEY}`,
              'Content-Type': 'application/json',
            },
          }
        );
        return retryResponse.data.choices[0].message.content.trim();
      } catch (retryError) {
        console.error('Retry failed:', retryError.response?.data || retryError.message);
        throw retryError;
      }
    }

    throw error;
  }
};

const translateStructure = async (data, targetLanguage) => {
  if (typeof data === 'object' && !Array.isArray(data)) {
    const translatedObject = {};
    for (const [key, value] of Object.entries(data)) {
      translatedObject[key] = await translateStructure(value, targetLanguage);
    }
    return translatedObject;
  } else if (Array.isArray(data)) {
    return Promise.all(data.map((element) => translateStructure(element, targetLanguage)));
  } else if (typeof data === 'string') {
    return translateText(data, targetLanguage);
  } else {
    return data;
  }
};

const findMissingKeys = (baseDict, targetDict) => {
  const missingKeys = {};
  for (const key in baseDict) {
    if (!(key in targetDict)) {
      missingKeys[key] = baseDict[key];
    } else if (typeof baseDict[key] === 'object' && typeof targetDict[key] === 'object') {
      const nestedMissing = findMissingKeys(baseDict[key], targetDict[key]);
      if (Object.keys(nestedMissing).length > 0) {
        missingKeys[key] = nestedMissing;
      }
    }
  }
  return missingKeys;
};

const deepUpdate = (original, updates) => {
  for (const key in updates) {
    if (typeof updates[key] === 'object' && key in original) {
      deepUpdate(original[key], updates[key]);
    } else {
      original[key] = updates[key];
    }
  }
};

const appendTranslations = async (rootFile, langFile, targetLanguage) => {
  try {
    // Load the root (en.json) and language-specific files
    const rootData = JSON.parse(fs.readFileSync(rootFile, 'utf-8'));
    const langData = JSON.parse(fs.readFileSync(langFile, 'utf-8'));

    // Find missing keys in the language file compared to the root
    const missingKeys = findMissingKeys(rootData, langData);

    if (Object.keys(missingKeys).length === 0) {
      console.log(`No missing keys found in ${langFile}`);
      return;
    }

    // Translate missing keys
    const translatedMissingKeys = await translateStructure(missingKeys, targetLanguage);

    // Append missing keys to the language file
    deepUpdate(langData, translatedMissingKeys);

    // Write updated translations back to the language file
    fs.writeFileSync(langFile, JSON.stringify(langData, null, 4), 'utf-8');

    console.log(`Updated ${langFile} with missing keys.`);
  } catch (error) {
    console.error(`Error updating ${langFile}:`, error);
  }
};

const main = async () => {
  const rootFile = 'src/locales/en.json';
  const langFiles = {
    Dutch: 'src/locales/nl.json',
    Spanish: 'src/locales/es.json',
    French: 'src/locales/fr.json',
  };

  console.log('Starting translation process...');
  console.log(`Using model: gpt-4o-mini`);

  for (const [language, langFile] of Object.entries(langFiles)) {
    console.log(`\nProcessing ${language} translations...`);
    try {
      await appendTranslations(rootFile, langFile, language);
    } catch (error) {
      console.error(`Failed to process ${language}:`, error.message);
      // Continue with other languages even if one fails
    }
  }

  console.log('\nTranslation process completed!');
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

main().catch(error => {
  console.error('Main process failed:', error);
  process.exit(1);
});
