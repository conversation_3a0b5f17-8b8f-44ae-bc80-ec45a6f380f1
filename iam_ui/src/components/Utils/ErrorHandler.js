/**
 * Error Handler Utility for OIDC Authentication
 * Provides centralized error handling for authentication flows
 */

import i18n from '@/plugins/i18'

/**
 * Maps backend error codes to user-friendly i18n keys
 */
const ERROR_CODE_MAP = {
  // Authentication Errors
  'authentication_failed': 'authentication_failed',
  'invalid_credentials': 'invalid_credentials',
  'invalid_token': 'invalid_token',
  'invalid_token_request': 'invalid_token_request',
  'authentication_expired': 'authentication_expired',
  'userinfo_error': 'userinfo_error',
  'invalid_userinfo_response': 'invalid_userinfo_response',
  'missing_email': 'missing_email',

  // Provider Errors
  'access_denied': 'access_denied',
  'provider_error': 'provider_error',
  'provider_configuration_error': 'provider_configuration_error',
  'provider_unavailable': 'provider_unavailable',
  'insufficient_permissions': 'insufficient_permissions',
  'rate_limited': 'rate_limited',

  // Validation Errors
  'email_verification_code_required': 'email_verification_code_required',
  'email_verification_session_not_found': 'email_verification_session_not_found',
  'invalid_email_verification_code': 'invalid_email_verification_code',
  'missing_acceptance_timestamp': 'missing_acceptance_timestamp',
  'missing_cookie_policy_version': 'missing_cookie_policy_version',
  'missing_privacy_policy_version': 'missing_privacy_policy_version',
  'missing_acceptable_use_policy_version': 'missing_acceptable_use_policy_version',
  'missing_terms_and_conditions_version': 'missing_terms_and_conditions_version',
  'invalid_first_name': 'invalid_first_name',
  'invalid_last_name': 'invalid_last_name',
  'invalid_password': 'invalid_password',
  'invalid_email_format': 'invalid_request',
  'email_already_used': 'user_already_exists_email',
  'invalid_phonenumber': 'invalid_request',
  'phone_already_used': 'invalid_request',
  'invalid_email_code': 'invalid_email_verification_code',
  'invalid_sms_code': 'invalid_email_verification_code',

  // System Errors
  'smsconfirmation_session_not_available': 'smsconfirmation_session_not_available',
  'emailconfirmation_session_not_available': 'emailconfirmation_session_not_available',

  // User Management Errors
  'user_already_exists_oidc': 'user_already_exists_oidc',
  'user_already_exists_email': 'user_already_exists_email',
  'user_not_found': 'user_not_found',
  'cannot_revoke_last_identity': 'cannot_revoke_last_identity',

  // Generic Errors
  'invalid_request': 'invalid_request',
  'network_error': 'network_error',
  'service_unavailable': 'service_unavailable',
  'session_expired': 'session_expired',
  'unknown_error': 'unknown_error'
}

/**
 * Maps HTTP status codes to appropriate error messages
 */
const STATUS_CODE_MAP = {
  400: 'invalid_request',
  401: 'authentication_failed',
  403: 'access_denied',
  404: 'authentication_failed', // Don't reveal if resource exists
  409: 'user_already_exists_email',
  422: 'invalid_request',
  429: 'rate_limited',
  500: 'service_unavailable',
  502: 'provider_unavailable',
  503: 'service_unavailable',
  504: 'provider_unavailable'
}

/**
 * Get user-friendly error message from response
 * @param {Object} response - Axios response object
 * @returns {string} - Translated error message
 */
export function getErrorMessage(response) {
  let errorKey = 'unknown_error'

  // Debug logging
  console.log('ErrorHandler - Processing response:', {
    response: response,
    data: response?.data,
    status: response?.status,
    error: response?.data?.error
  })

  if (response && response.data && response.data.error) {
    // Use backend error code if available
    const backendError = response.data.error
    errorKey = ERROR_CODE_MAP[backendError] || 'unknown_error'
    console.log('ErrorHandler - Using backend error:', backendError, '-> mapped to:', errorKey)
  } else if (response && response.status) {
    // Fall back to HTTP status code mapping
    errorKey = STATUS_CODE_MAP[response.status] || 'unknown_error'
    console.log('ErrorHandler - Using status code:', response.status, '-> mapped to:', errorKey)
  }

  const translatedMessage = i18n.global.t(errorKey)
  console.log('ErrorHandler - Final message:', translatedMessage)
  return translatedMessage
}

/**
 * Get error parameters from URL
 * @returns {Object} - Object with error and error_description
 */
export function getUrlErrorParams() {
  const urlParams = new URLSearchParams(window.location.search)
  return {
    error: urlParams.get('error'),
    error_description: urlParams.get('error_description')
  }
}

/**
 * Clear error parameters from URL without page reload
 */
export function clearUrlErrorParams() {
  const url = new URL(window.location)
  url.searchParams.delete('error')
  url.searchParams.delete('error_description')
  window.history.replaceState({}, document.title, url.toString())
}

/**
 * Handle URL error parameters and show appropriate message
 * @param {Function} showInfoCallback - Callback to show error message
 * @returns {boolean} - True if error was found and handled
 */
export function handleUrlErrors(showInfoCallback) {
  const { error, error_description } = getUrlErrorParams()

  if (error) {
    // Map URL error to user-friendly message
    let errorKey = ERROR_CODE_MAP[error]
    let message

    if (errorKey) {
      // Use our translated message if we have one
      message = i18n.global.t(errorKey)
    } else if (error_description) {
      // Fall back to error_description if provided and we don't have a translation
      message = error_description
    } else {
      // Final fallback to generic authentication failed message
      message = i18n.global.t('authentication_failed')
    }

    // Show the error message
    if (showInfoCallback) {
      showInfoCallback('error', message)
    }

    // Clear the error parameters from URL
    clearUrlErrorParams()

    return true
  }

  return false
}

/**
 * Enhanced error handler for authentication flows
 * @param {Object} response - Axios response object
 * @param {Function} showInfoCallback - Callback to show error message
 */
export function handleAuthError(response, showInfoCallback) {
  const message = getErrorMessage(response)

  if (showInfoCallback) {
    showInfoCallback('error', message)
  }

  // Optional: Log error for debugging (in development)
  if (process.env.NODE_ENV === 'development') {
    console.error('Auth Error:', {
      status: response?.status,
      error: response?.data?.error,
      message: message,
      response: response
    })
  }
}

export default {
  getErrorMessage,
  getUrlErrorParams,
  clearUrlErrorParams,
  handleUrlErrors,
  handleAuthError
}
