<template>
  <data-table 
    v-model="page" 
    :headers="headers"
    :items="providers"
    class="elevation-1"
    @refresh="load"
    :loading="dataLoading"
  >
    <template v-slot:[`item.lastlogin`]="{ item }">
      {{ formatDate(item.lastlogin) }}
    </template>
    <template v-slot:[`item.actions`]="{ item }">
      <v-icon color="red" size="small" @click="revokeItem(item)">
        mdi-delete
      </v-icon>
    </template>
    <template v-slot:no-data>
      <div class="text-center pa-4">
        {{ $t('no-oidc-providers') }}
      </div>
    </template>
  </data-table>
</template>

<script>
import SettingsStrings from "./SettingsStrings.js"
import { handleAuthError } from "../../Utils/ErrorHandler"
export default {
  data() {
    return {
      page: 1,
      dataLoading: true,
      headers: [
        { title: this.$t('provider'), align: 'start', sortable: true, value: 'provider_name' },
        { title: this.$t('email'), align: 'start', sortable: true, value: 'email' },
        { title: this.$t('last-login'), align: 'start', sortable: true, value: 'lastlogin' },
        { title: this.$t('actions'), value: 'actions', sortable: false },
      ],
      providers: []
    }
  },
  created: function () {
    this.load()
  },
  methods: {
    async load() {
      this.dataLoading = true
      try {
        const response = await this.axios.get(`/api/users/${this.username}/oidc_providers`)
        this.providers = response.data || []
      } catch (error) {
        console.error(error)
        this.alert('error', this.$t('something-went-wrong-please-try-again-later'))
      } finally {
        this.dataLoading = false
      }
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
    },
    revokeItem(item) {
      const provider_name = item.provider_name
      const provider_id = item.provide
      const sub = item.subject
      this.alert("confirm", SettingsStrings({provider: provider_name}, "revoke_oidc_provider"), undefined, async () => {
        try {
          await this.axios.delete(`/api/users/${this.username}/oidc_providers/${provider_id}`, {
            data: { sub: sub }
          })
          this.alert('info', this.$t('oidc-provider-revoked-successfully'))
          this.load()
        } catch (error) {
          handleAuthError(error.response, (type, message) => {
            this.alert(type, message)
          })
        }
      })
    },
  },
}
</script>

<style scoped>
.v-data-table {
  width: fit-content;
  max-width: 100%;
}
</style>
